# Console commands to update TCPL company's "Urgent" priorities to "High"
# Run these commands one by one in Rails console

# Step 1: Find the company
company = Company.find_by(subdomain: 'tcpl')
puts "Company: #{company.name}" if company

# Step 2: Check current urgent tickets
urgent_tickets = company.help_tickets.where("LOWER(priority) = ?", 'urgent')
puts "Tickets with 'urgent' priority in help_tickets table: #{urgent_tickets.count}"

urgent_cfv = CustomFormValue.joins(:custom_form_field)
                           .where(company_id: company.id)
                           .where(custom_form_fields: { name: 'priority', field_attribute_type: 'priority' })
                           .where("LOWER(value_str) = ?", 'urgent')
puts "Custom form values with 'urgent' priority: #{urgent_cfv.count}"

# Step 3: Get ticket numbers for reference
urgent_ticket_numbers = urgent_cfv.joins(:help_ticket).pluck('help_tickets.ticket_number').compact
puts "Urgent ticket numbers: #{urgent_ticket_numbers}"

# Step 4: Perform the update (run this in a transaction)
ActiveRecord::Base.transaction do
  # Update help_tickets table
  updated_tickets = company.help_tickets.where("LOWER(priority) = ?", 'urgent').update_all(priority: 'high')
  puts "Updated #{updated_tickets} tickets in help_tickets table"
  
  # Update custom_form_values table
  updated_cfv = CustomFormValue.joins(:custom_form_field)
                              .where(company_id: company.id)
                              .where(custom_form_fields: { name: 'priority', field_attribute_type: 'priority' })
                              .where("LOWER(value_str) = ?", 'urgent')
                              .update_all(value_str: 'high')
  puts "Updated #{updated_cfv} custom form values"
  
  # Verify the update
  remaining_urgent_tickets = company.help_tickets.where("LOWER(priority) = ?", 'urgent').count
  remaining_urgent_cfv = CustomFormValue.joins(:custom_form_field)
                                       .where(company_id: company.id)
                                       .where(custom_form_fields: { name: 'priority', field_attribute_type: 'priority' })
                                       .where("LOWER(value_str) = ?", 'urgent')
                                       .count
  
  puts "Verification - Remaining urgent tickets: #{remaining_urgent_tickets}"
  puts "Verification - Remaining urgent custom form values: #{remaining_urgent_cfv}"
  
  if remaining_urgent_tickets == 0 && remaining_urgent_cfv == 0
    puts "✅ SUCCESS: All urgent priorities updated to high!"
  else
    puts "⚠️ Some urgent priorities may still remain"
  end
end
