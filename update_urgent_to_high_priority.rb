#!/usr/bin/env ruby

# Script to update all "Urgent" priority tickets to "High" priority for TCPL company
# This script updates both the help_tickets.priority column and custom_form_values table

puts "Starting priority update script for TCPL company..."

# Find the TCPL company
company = Company.find_by(subdomain: 'tcpl')

if company.nil?
  puts "ERROR: Company with subdomain 'tcpl' not found!"
  exit 1
end

puts "Found company: #{company.name} (ID: #{company.id})"

# Find all help tickets for this company with "Urgent" priority
urgent_tickets_direct = company.help_tickets.where(priority: 'urgent')
urgent_tickets_direct_count = urgent_tickets_direct.count

puts "Found #{urgent_tickets_direct_count} tickets with 'urgent' priority in help_tickets table"

# Find tickets with "Urgent" priority in custom_form_values
urgent_custom_form_values = CustomFormValue.joins(:custom_form_field, :custom_form)
                                          .where(company_id: company.id)
                                          .where(custom_form_fields: { name: 'priority', field_attribute_type: 'priority' })
                                          .where("LOWER(value_str) = ?", 'urgent')

urgent_custom_form_values_count = urgent_custom_form_values.count
puts "Found #{urgent_custom_form_values_count} tickets with 'urgent' priority in custom_form_values table"

# Get the ticket IDs from custom form values for verification
urgent_ticket_ids_from_cfv = urgent_custom_form_values.pluck(:module_id).compact

puts "Ticket IDs with urgent priority from custom form values: #{urgent_ticket_ids_from_cfv}"

# Start the update process
updated_count = 0
cfv_updated_count = 0

ActiveRecord::Base.transaction do
  begin
    # Update help_tickets.priority column
    if urgent_tickets_direct_count > 0
      updated_count = urgent_tickets_direct.update_all(priority: 'high')
      puts "Updated #{updated_count} tickets in help_tickets table from 'urgent' to 'high'"
    end

    # Update custom_form_values
    if urgent_custom_form_values_count > 0
      cfv_updated_count = urgent_custom_form_values.update_all(value_str: 'high')
      puts "Updated #{cfv_updated_count} custom form values from 'urgent' to 'high'"
    end

    # Create activity logs for the updated tickets
    urgent_ticket_ids_from_cfv.each do |ticket_id|
      ticket = HelpTicket.find_by(id: ticket_id)
      next unless ticket

      # Create activity log for priority change
      HelpTicketActivity.create!(
        help_ticket_id: ticket.id,
        owner_id: nil,
        activity_type: 'priority',
        activity_action: 'ticket_update',
        data: {
          previous_priority: 'urgent',
          current_priority: 'high',
          updated_by: 'System Script',
          updated_at: Time.current
        }
      )
    end

    puts "Created activity logs for #{urgent_ticket_ids_from_cfv.length} tickets"

    # Verification: Check if any urgent priorities remain
    remaining_urgent_direct = company.help_tickets.where(priority: 'urgent').count
    remaining_urgent_cfv = CustomFormValue.joins(:custom_form_field, :custom_form)
                                         .where(company_id: company.id)
                                         .where(custom_form_fields: { name: 'priority', field_attribute_type: 'priority' })
                                         .where("LOWER(value_str) = ?", 'urgent')
                                         .count

    puts "\n=== VERIFICATION ==="
    puts "Remaining 'urgent' priorities in help_tickets table: #{remaining_urgent_direct}"
    puts "Remaining 'urgent' priorities in custom_form_values table: #{remaining_urgent_cfv}"

    if remaining_urgent_direct == 0 && remaining_urgent_cfv == 0
      puts "\n✅ SUCCESS: All 'urgent' priorities have been successfully updated to 'high'"
      puts "Total tickets updated: #{[updated_count, cfv_updated_count].max}"
    else
      puts "\n⚠️  WARNING: Some 'urgent' priorities may still remain"
    end

  rescue => e
    puts "\n❌ ERROR occurred during update: #{e.message}"
    puts e.backtrace.first(5)
    raise # This will rollback the transaction
  end
end

puts "\n=== SUMMARY ==="
puts "Company: #{company.name} (#{company.subdomain})"
puts "Help tickets table updates: #{updated_count}"
puts "Custom form values updates: #{cfv_updated_count}"
puts "Activity logs created: #{urgent_ticket_ids_from_cfv.length}"
puts "\nScript completed successfully!"
